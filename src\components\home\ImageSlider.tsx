import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface SliderItem {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
}

const ImageSlider: React.FC = () => {
  const initialItems: SliderItem[] = [
    {
      id: 1,
      title: "Discover Tanzania",
      description: "Embark on an extraordinary journey through Tanzania's diverse landscapes, from vast savannas to pristine coastlines, discovering the heart of East Africa.",
      imageUrl: "https://images.unsplash.com/photo-1575999502951-4ab25b5ca889?w=1200&h=800&fit=crop&crop=center"
    },
    {
      id: 2,
      title: "Serengeti National Park",
      description: "Witness the greatest wildlife spectacle on Earth in the endless plains of Serengeti, home to the Great Migration and abundant wildlife.",
      imageUrl: "https://images.unsplash.com/photo-1580145575237-75fec2a0320b?w=1200&h=800&fit=crop&crop=center"
    },
    {
      id: 3,
      title: "Ngorongoro Crater",
      description: "Explore the world's largest intact volcanic caldera, a natural amphitheater teeming with wildlife in this UNESCO World Heritage Site.",
      imageUrl: "https://images.unsplash.com/photo-1697062272790-0bcdf46539c0?w=1200&h=800&fit=crop&crop=center"
    },
    {
      id: 4,
      title: "Mount Kilimanjaro",
      description: "Conquer Africa's highest peak and stand atop the roof of Africa, experiencing diverse ecosystems from rainforest to arctic summit.",
      imageUrl: "https://images.unsplash.com/photo-1551632811-561732d1e306?w=1200&h=800&fit=crop&crop=center"
    },
    {
      id: 5,
      title: "Zanzibar Island",
      description: "Relax on pristine white sand beaches and explore the historic Stone Town in this tropical island paradise off Tanzania's coast.",
      imageUrl: "https://images.unsplash.com/photo-1549035092-33b2937b075a?w=1200&h=800&fit=crop&crop=center"
    }
  ];

  const [items, setItems] = useState<SliderItem[]>(initialItems);

  const handleNext = () => {
    setItems(prev => {
      const newItems = [...prev];
      const firstItem = newItems.shift()!;
      newItems.push(firstItem);
      return newItems;
    });
  };

  const handlePrev = () => {
    setItems(prev => {
      const newItems = [...prev];
      const lastItem = newItems.pop()!;
      newItems.unshift(lastItem);
      return newItems;
    });
  };

  return (
    <div className="relative w-full h-screen overflow-hidden" aria-label="Safari experiences slider">
      <style jsx global>{`
        .slider-item {
          width: 200px;
          height: 300px;
          list-style-type: none;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          z-index: 1;
          background-position: center;
          background-size: cover;
          border-radius: 20px;
          box-shadow: 0 20px 30px rgba(212, 194, 164, 0.3) inset;
          transition: transform 0.1s, left 0.75s, top 0.75s, width 0.75s, height 0.75s;
        }
        
        .slider-item:nth-child(1),
        .slider-item:nth-child(2) {
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          transform: none;
          border-radius: 0;
          box-shadow: none;
          opacity: 1;
        }
        
        .slider-item:nth-child(3) {
          left: 50%;
        }
        
        .slider-item:nth-child(4) {
          left: calc(50% + 220px);
        }
        
        .slider-item:nth-child(5) {
          left: calc(50% + 440px);
        }
        
        .slider-item:nth-child(6) {
          left: calc(50% + 660px);
          opacity: 0;
        }
        
        .slider-content {
          width: min(30vw, 400px);
          position: absolute;
          top: 50%;
          left: 3rem;
          transform: translateY(-50%);
          font-family: 'Inter', sans-serif;
          color: #F2EEE6;
          text-shadow: 0 3px 8px rgba(0,0,0,0.7);
          opacity: 0;
          display: none;
        }
        
        .slider-content .title {
          font-family: 'Inter', sans-serif;
          font-weight: 700;
          font-size: 2.5rem;
          text-transform: uppercase;
          color: #D4C2A4;
          margin-bottom: 1rem;
        }
        
        .slider-content .description {
          line-height: 1.7;
          margin: 1rem 0 1.5rem;
          font-size: 1rem;
          color: #F2EEE6;
        }
        
        .slider-content button {
          width: fit-content;
          background-color: rgba(212, 194, 164, 0.1);
          color: #D4C2A4;
          border: 2px solid #D4C2A4;
          border-radius: 0.5rem;
          padding: 0.75rem 1.5rem;
          cursor: pointer;
          font-family: 'Inter', sans-serif;
          font-weight: 500;
          transition: all 0.3s ease;
        }
        
        .slider-content button:hover {
          background-color: #D4C2A4;
          color: #16191D;
        }
        
        .slider-item:nth-of-type(2) .slider-content {
          display: block;
          animation: show 0.75s ease-in-out 0.3s forwards;
        }
        
        @keyframes show {
          0% {
            filter: blur(5px);
            transform: translateY(calc(-50% + 75px));
          }
          100% {
            opacity: 1;
            filter: blur(0);
          }
        }
        
        .nav {
          position: absolute;
          bottom: 2rem;
          left: 50%;
          transform: translateX(-50%);
          z-index: 5;
          user-select: none;
        }
        
        .nav .btn {
          background-color: rgba(212, 194, 164, 0.2);
          color: #D4C2A4;
          border: 2px solid rgba(212, 194, 164, 0.6);
          margin: 0 0.25rem;
          padding: 0.75rem;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
        }
        
        .nav .btn:hover {
          background-color: rgba(212, 194, 164, 0.4);
          border-color: #D4C2A4;
        }
        
        @media (width > 650px) and (width < 900px) {
          .slider-content .title { font-size: 2rem; }
          .slider-content .description { font-size: 0.9rem; }
          .slider-content button { font-size: 0.9rem; }
          
          .slider-item {
            width: 160px;
            height: 270px;
          }
          
          .slider-item:nth-child(3) { left: 50%; }
          .slider-item:nth-child(4) { left: calc(50% + 170px); }
          .slider-item:nth-child(5) { left: calc(50% + 340px); }
          .slider-item:nth-child(6) { left: calc(50% + 510px); opacity: 0; }
        }
        
        @media (width < 650px) {
          .slider-content .title { font-size: 1.5rem; }
          .slider-content .description { font-size: 0.8rem; }
          .slider-content button { font-size: 0.8rem; }
          
          .slider-item {
            width: 130px;
            height: 220px;
          }
          
          .slider-item:nth-child(3) { left: 50%; }
          .slider-item:nth-child(4) { left: calc(50% + 140px); }
          .slider-item:nth-child(5) { left: calc(50% + 280px); }
          .slider-item:nth-child(6) { left: calc(50% + 420px); opacity: 0; }
        }
      `}</style>
      
      <div className="h-full w-full grid place-items-center">
        <main className="relative w-full h-full">
          <ul className="slider">
            {items.map((item, index) => (
              <li
                key={item.id}
                className="slider-item"
                style={{ backgroundImage: `url('${item.imageUrl}')` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/20 to-transparent" />
                <div className="slider-content">
                  <h2 className="title">"{item.title}"</h2>
                  <p className="description">{item.description}</p>
                  <button>Explore More</button>
                </div>
              </li>
            ))}
          </ul>
          
          <nav className="nav">
            <button className="btn prev" onClick={handlePrev} aria-label="Previous slide">
              <ChevronLeft size={20} />
            </button>
            <button className="btn next" onClick={handleNext} aria-label="Next slide">
              <ChevronRight size={20} />
            </button>
          </nav>
        </main>
      </div>
    </div>
  );
};

export default ImageSlider;