import React from 'react';

const ImageTest = () => {
  const testImages = [
    'https://images.unsplash.com/photo-1516426122078-c23e76319801?w=800&h=600&fit=crop&q=80',
    'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800&h=600&fit=crop&q=80',
    'https://images.unsplash.com/photo-1551632811-561732d1e306?w=800&h=600&fit=crop&q=80'
  ];

  return (
    <div className="p-8 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Image Loading Test</h1>
      
      <div className="grid grid-cols-3 gap-4 mb-8">
        {testImages.map((src, index) => (
          <div key={index} className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-2">Test Image {index + 1}</h3>
            <div className="w-full h-48 bg-gray-200 rounded overflow-hidden">
              <img 
                src={src} 
                alt={`Test image ${index + 1}`}
                className="w-full h-full object-cover"
                onLoad={() => console.log(`Image ${index + 1} loaded successfully`)}
                onError={(e) => {
                  console.error(`Image ${index + 1} failed to load:`, src);
                  e.currentTarget.src = 'https://placehold.co/400x300/cccccc/666666?text=Failed+to+Load';
                }}
              />
            </div>
            <p className="text-sm text-gray-600 mt-2 break-all">{src}</p>
          </div>
        ))}
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-2">Background Image Test</h3>
        <div 
          className="w-full h-48 bg-gray-200 rounded bg-cover bg-center"
          style={{ backgroundImage: `url(${testImages[0]})` }}
        >
          <div className="w-full h-full bg-black bg-opacity-50 flex items-center justify-center">
            <span className="text-white text-xl font-bold">Background Image</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageTest;
