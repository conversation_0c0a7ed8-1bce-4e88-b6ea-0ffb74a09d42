import React from 'react';
import { Link } from 'react-router-dom';

const HeroSection = () => {
  return (
    <section className="hero-section">
      {/* Background Video */}
      <video
        className="hero-video"
        autoPlay
        muted
        loop
        playsInline
      >
        <source src="https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/NEW%20SHIT/Generated%20File%20June%2024,%202025%20-%208_51AM.mp4" type="video/mp4" />
        {/* Fallback for browsers that don't support video */}
        Your browser does not support the video tag.
      </video>

      {/* Video Overlay */}
      <div className="hero-overlay"></div>

      <div className="hero-content">
        {/* Logo and Company Name Combined */}
        <div className="flex flex-col items-center mb-8">
          <img
            src="/photos/heroLogo.svg"
            alt="Warriors of Africa Safari Logo"
            className="hero-logo mb-4"
          />
          <div className="hero-company-name">
            WARRIORS OF <br />
            AFRICA SAFARI
          </div>
        </div>

        {/* Main Headline */}
        <h1 className="hero-headline">
          Welcome to the<br />
          Land of <em>Endless</em><br />
          Safari
        </h1>

        {/* Call-to-Action Button */}
        <Link to="/tour-builder" className="hero-cta-button">
          JOIN THE SAFARI
        </Link>
      </div>
    </section>
  );
};

export default HeroSection;
