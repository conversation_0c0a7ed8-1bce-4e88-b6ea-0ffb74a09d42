import React, { useLayoutEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

const ValuePropositions: React.FC = () => {
  const componentRef = useRef<HTMLDivElement>(null);
  const scrollDistRef = useRef<HTMLDivElement>(null);
  const skyRef = useRef<SVGImageElement>(null);
  const cloud1Ref = useRef<SVGImageElement>(null);
  const cloud1MaskRef = useRef<SVGGElement>(null);
  const cloud2Ref = useRef<SVGImageElement>(null);
  const cloud3Ref = useRef<SVGImageElement>(null);
  const mountBgRef = useRef<SVGImageElement>(null);
  const mountMgRef = useRef<SVGImageElement>(null);
  const mountFgRef = useRef<SVGImageElement>(null);
  const arrowRef = useRef<SVGPolylineElement>(null);
  const arrowBtnRef = useRef<SVGRectElement>(null);

  useLayoutEffect(() => {
    const ctx = gsap.context(() => {
      ScrollTrigger.matchMedia({
        // --- DESKTOP SETUP (screens wider than 768px) ---
        '(min-width: 768px)': function() {
          if (scrollDistRef.current) {
            // Use a long scroll distance for a slow parallax effect
            scrollDistRef.current.style.height = '3000px';
          }

          const tl = gsap.timeline({
            scrollTrigger: {
              trigger: scrollDistRef.current,
              start: 'top bottom',
              end: 'bottom top',
              scrub: 1,
            },
          });

          // Original, larger parallax movements for desktop
          tl.fromTo(skyRef.current, { y: 0 }, { y: -200 }, 0)
            .fromTo(cloud1Ref.current, { y: 100 }, { y: -800 }, 0)
            .fromTo(cloud1MaskRef.current, { y: 100 }, { y: -800 }, 0)
            .fromTo(cloud2Ref.current, { y: -150 }, { y: -500 }, 0)
            .fromTo(cloud3Ref.current, { y: -50 }, { y: -650 }, 0)
            .fromTo(mountBgRef.current, { y: -10 }, { y: -100 }, 0)
            .fromTo(mountMgRef.current, { y: -30 }, { y: -250 }, 0)
            .fromTo(mountFgRef.current, { y: -50 }, { y: -600 }, 0);
        },

        // --- MOBILE SETUP (screens narrower than 768px) ---
        '(max-width: 767px)': function() {
          if (scrollDistRef.current) {
            // Set scroll distance relative to the viewport height for a consistent feel
            scrollDistRef.current.style.height = '200vh';
          }

          const tl = gsap.timeline({
            scrollTrigger: {
              trigger: scrollDistRef.current,
              start: 'top bottom',
              end: 'bottom top',
              scrub: 1,
            },
          });

          // Adjusted, more subtle parallax movements for mobile screens
          tl.fromTo(skyRef.current, { y: 0 }, { y: -100 }, 0)
            .fromTo(cloud1Ref.current, { y: 50 }, { y: -400 }, 0)
            .fromTo(cloud1MaskRef.current, { y: 50 }, { y: -400 }, 0)
            .fromTo(cloud2Ref.current, { y: -75 }, { y: -250 }, 0)
            .fromTo(cloud3Ref.current, { y: -25 }, { y: -325 }, 0)
            .fromTo(mountBgRef.current, { y: -5 }, { y: -50 }, 0)
            .fromTo(mountMgRef.current, { y: -15 }, { y: -125 }, 0)
            .fromTo(mountFgRef.current, { y: -25 }, { y: -300 }, 0);
        },
      });

      // --- SHARED INTERACTIVITY (for all screen sizes) ---
      const arrowBtn = arrowBtnRef.current;
      const arrow = arrowRef.current;

      const handleMouseEnter = () => gsap.to(arrow, { y: 10, duration: 0.8, ease: 'back.inOut(3)', overwrite: 'auto' });
      const handleMouseLeave = () => gsap.to(arrow, { y: 0, duration: 0.5, ease: 'power3.out', overwrite: 'auto' });
      const handleClick = () => {
        const nextSection = scrollDistRef.current?.nextElementSibling;
        gsap.to(window, {
          scrollTo: { y: nextSection || `+=${window.innerHeight}`, autoKill: false },
          duration: 1.5,
          ease: 'power1.inOut',
        });
      };

      if (arrowBtn) {
        arrowBtn.addEventListener('mouseenter', handleMouseEnter);
        arrowBtn.addEventListener('mouseleave', handleMouseLeave);
        arrowBtn.addEventListener('click', handleClick);
        return () => { // Cleanup event listeners
          arrowBtn.removeEventListener('mouseenter', handleMouseEnter);
          arrowBtn.removeEventListener('mouseleave', handleMouseLeave);
          arrowBtn.removeEventListener('click', handleClick);
        };
      }
    }, componentRef);

    return () => ctx.revert(); // Cleanup GSAP context and all animations
  }, []);

  return (
    // On mobile, remove padding (py-0) to allow the component to fill the screen
    <div ref={componentRef} className="w-full font-['Montserrat',sans-serif] text-center md:py-16 bg-[#16191D]">
      <div ref={scrollDistRef} className="relative w-full">
        {/*
          - On mobile: occupy the full screen (h-screen) and stick to the top (top-0)
          - On desktop (md): use a fixed height (md:h-[800px]) and stick 16px from the top (md:top-16)
        */}
        <main className="sticky w-full max-w-[1200px] mx-auto overflow-hidden h-screen md:h-[800px] top-0 md:top-16 md:rounded-lg bg-[#16191D]">
          <svg
            viewBox="0 0 1200 800"
            xmlns="http://www.w3.org/2000/svg"
            className="w-full h-full"
            preserveAspectRatio="xMidYMid slice" // Ensures SVG covers the container without distortion
          >
            <defs>
              <mask id="m">
                <g ref={cloud1MaskRef}>
                  <rect fill="#fff" width="100%" height="801" y="799" />
                  <image xlinkHref="https://assets.codepen.io/721952/cloud1Mask.jpg" width="1200" height="800" />
                </g>
              </mask>
            </defs>

            <image ref={skyRef} xlinkHref="https://assets.codepen.io/721952/sky.jpg" width="1200" height="590" />
            <image ref={mountBgRef} xlinkHref="https://assets.codepen.io/721952/mountBg.png" width="1200" height="800" />
            <image ref={mountMgRef} xlinkHref="https://assets.codepen.io/721952/mountMg.png" width="1200" height="800" />
            <image ref={cloud2Ref} xlinkHref="https://assets.codepen.io/721952/cloud2.png" width="1200" height="800" />
            <image ref={mountFgRef} xlinkHref="https://assets.codepen.io/721952/mountFg.png" width="1200" height="800" />
            <image ref={cloud1Ref} xlinkHref="https://assets.codepen.io/721952/cloud1.png" width="1200" height="800" />
            <image ref={cloud3Ref} xlinkHref="https://assets.codepen.io/721952/cloud3.png" width="1200" height="800" />
            
            {/* Centered text using x="50%" and text-anchor="middle" */}
            <text fill="#F2EEE6" x="50%" y="350" textAnchor="middle" className="text-5xl sm:text-7xl md:text-9xl lg:text-[120px] font-black font-cormorant">
              EXPLORE
            </text>

            {/* Arrow positioned relative to the centered text */}
            <polyline ref={arrowRef} fill="#D4C2A4" points="599,400 599,439 590,429 590,432 600,442 610,432 610,429 601,439 601,400" />

            <g mask="url(#m)">
              <rect fill="#D4C2A4" width="100%" height="100%" />
              <text x="50%" y="350" fill="#16191D" textAnchor="middle" className="text-5xl sm:text-7xl md:text-9xl lg:text-[120px] font-black font-cormorant "  style={{
                             textShadow: '1px 1px 0px rgba(212,194,164,0.4), 2px 2px 0px rgba(212,194,164,0.3), 3px 3px 0px rgba(212,194,164,0.2), 4px 4px 0px rgba(212,194,164,0.1)'
                         }}>
                FURTHER
              </text>
            </g>
             
            {/* Invisible button positioned over the arrow */}
            <rect ref={arrowBtnRef} width="100" height="100" opacity="0" x="550" y="370" className="cursor-pointer" />
          </svg>
        </main>
      </div>
      {/* You can add content below the animation here, and the scroll-to button will navigate to it */}
    </div>
  );
};

export default ValuePropositions;