<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaveScroll Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        *, *:before, *:after {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }

        body {
          overflow: hidden;
          background: #000;
        }

        .ws-pages {
          overflow: hidden;
          position: relative;
          height: 100vh;
        }

        .ws-bgs {
          position: relative;
          height: 100%;
        }

        .ws-bg {
          display: flex;
          height: 100%;
          background-size: cover;
          background-position: center center;
        }

        .ws-pages.s--ready .ws-bg {
          background: none;
        }

        .ws-bg__part {
          overflow: hidden;
          position: relative;
          height: 100%;
          cursor: grab;
          user-select: none;
        }

        .ws-bg__part-inner {
          position: absolute;
          top: 0;
          width: 100vw;
          height: 100%;
          background-size: cover;
          background-position: center center;
        }

        .ws-bg:nth-child(1) {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-4.jpg);
        }

        .ws-bg:nth-child(1) .ws-bg__part-inner {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-4.jpg);
        }

        .ws-bg:nth-child(2) {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-5.jpg);
        }

        .ws-bg:nth-child(2) .ws-bg__part-inner {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-5.jpg);
        }

        .ws-bg:nth-child(3) {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-6.jpg);
        }

        .ws-bg:nth-child(3) .ws-bg__part-inner {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-6.jpg);
        }

        .ws-bg:nth-child(4) {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-7.jpg);
        }

        .ws-bg:nth-child(4) .ws-bg__part-inner {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-7.jpg);
        }

        .ws-bg:nth-child(5) {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-8.jpg);
        }

        .ws-bg:nth-child(5) .ws-bg__part-inner {
          background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/142996/onepgscr-8.jpg);
        }

        .ws-text {
          overflow: hidden;
          position: absolute;
          left: 15%;
          top: 50%;
          width: 70%;
          height: 50px;
          margin-top: -25px;
          pointer-events: none;
        }

        .ws-text__heading {
          display: flex;
          justify-content: space-between;
          height: 100%;
          font-size: 30px;
          line-height: 50px;
          color: #fff;
        }
    </style>
</head>
<body>
    <div class="ws-pages">
      <div class="ws-bgs">
        <div class="ws-bg"></div>
        <div class="ws-bg"></div>
        <div class="ws-bg"></div>
        <div class="ws-bg"></div>
        <div class="ws-bg"></div>
      </div>
      <div class="ws-text">
        <h2 class="ws-text__heading">
          <span>Page 1</span>
          <span>Drag image parts or just scroll down</span>
        </h2>
        <h2 class="ws-text__heading">
          <span>Page 2</span>
        </h2>
        <h2 class="ws-text__heading">
          <span>Page 3</span>
          <span>Still nothing</span>
        </h2>
        <h2 class="ws-text__heading">
          <span>Page 4</span>
        </h2>
        <h2 class="ws-text__heading">
          <span>Page 5</span>
          <span>The end</span>
        </h2>
      </div>
    </div>

    <script>
        // Original JavaScript code here
        window.requestAnimFrame = (function() {
          return window.requestAnimationFrame ||
            window.webkitRequestAnimationFrame ||
            window.mozRequestAnimationFrame ||
            function(callback){
            window.setTimeout(callback, 1000 / 60);
          };
        })();

        function rafThrottle(fn) {
          var busy = false;
          return function() {
            if (busy) return;
            busy = true;
            fn.apply(this, arguments);
            requestAnimFrame(function() {
              busy = false;
            });
          };
        };

        $(document).ready(function() {
          // Original JavaScript implementation
          console.log("WaveScroll test loaded");
        });
    </script>
</body>
</html>
