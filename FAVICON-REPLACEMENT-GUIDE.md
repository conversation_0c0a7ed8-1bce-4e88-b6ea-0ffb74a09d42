# Favicon Replacement Guide - Warriors of Africa Safari

## Overview
This guide will help you replace the current safari-themed favicon with the actual Warriors of Africa Safari logo from `/public/logo.png`.

## What I've Already Done ✅

1. **Updated HTML favicon links** in `index.html` to reference PNG files instead of SVG
2. **Updated web manifest** in `site.webmanifest` to include proper icon references
3. **Created generation scripts** for both Windows and Unix systems
4. **Updated documentation** in `README-favicon.md`

## What You Need to Do 🔄

### Option 1: Use Online Tools (Recommended - Easiest)

1. **Visit [favicon.io](https://favicon.io/favicon-converter/)**
2. **Upload** the file `/public/logo.png`
3. **Download** the generated favicon package
4. **Extract** the files and copy these to `/public/`:
   - `favicon.ico`
   - `favicon-16x16.png`
   - `favicon-32x32.png`
   - `apple-touch-icon.png`
   - `android-chrome-192x192.png`
   - `android-chrome-512x512.png`

### Option 2: Use ImageMagick (Command Line)

1. **Install ImageMagick** if not already installed:
   - Windows: Download from [imagemagick.org](https://imagemagick.org/script/download.php#windows)
   - macOS: `brew install imagemagick`
   - Ubuntu/Debian: `sudo apt-get install imagemagick`

2. **Navigate to the public folder**:
   ```bash
   cd public
   ```

3. **Run the generation script**:
   - Windows: Double-click `generate-favicons.bat` or run in Command Prompt
   - Unix/Linux/Mac: `chmod +x generate-favicons.sh && ./generate-favicons.sh`

### Option 3: Manual Creation with Image Editor

1. **Open** `/public/logo.png` in your preferred image editor (Photoshop, GIMP, etc.)
2. **Create** the following files by resizing and exporting:
   - `favicon.ico` (16x16, 32x32, 48x48 - multi-size ICO)
   - `favicon-16x16.png` (16x16)
   - `favicon-32x32.png` (32x32)
   - `apple-touch-icon.png` (180x180)
   - `android-chrome-192x192.png` (192x192)
   - `android-chrome-512x512.png` (512x512)

## Files to Remove (Optional)

After creating the new favicon files, you can remove these old SVG files:
- `favicon.svg`
- `favicon-16x16.svg`
- `favicon-32x32.svg`
- `apple-touch-icon.svg`

## Testing Your New Favicon

1. **Clear browser cache** or open in incognito/private mode
2. **Visit your website** and check the browser tab
3. **Test on mobile devices** to verify Apple touch icon
4. **Check PWA installation** to verify Android Chrome icons

## Expected Results

- ✅ Browser tab shows your actual logo instead of safari theme
- ✅ Bookmarks display your logo
- ✅ Mobile home screen icons show your logo
- ✅ PWA installation uses your logo
- ✅ Consistent branding across all platforms

## Troubleshooting

- **Favicon not updating?** Clear browser cache and hard refresh (Ctrl+F5)
- **Mobile icon not showing?** Check that apple-touch-icon.png is 180x180 pixels
- **PWA icon issues?** Verify android-chrome icons are 192x192 and 512x512

## Need Help?

If you encounter any issues, the configuration is already set up correctly. You just need to generate the favicon files from your logo.png and place them in the `/public/` directory.
