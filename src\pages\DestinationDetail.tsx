import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  MapPin,
  Star,
  Clock,
  Camera,
  Thermometer,
  Shield,
  Users,
  Calendar,
  ArrowLeft,
  Plane,
  TreePine,
  Heart,
  Info,
  Award,
  Globe,
  Compass,
  BookOpen
} from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Destination } from '@/types/firebase';
import { useQuery } from '@tanstack/react-query';

const DestinationDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Use React Query for better caching and performance
  const { data: destination, isLoading: loading, error } = useQuery({
    queryKey: ['destination', id],
    queryFn: () => id ? FirebaseService.getDestination(id) : null,
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  if (loading) {
    return (
      <PageLoader
        title="Loading Destination..."
        subtitle="Discovering this amazing location for you..."
      />
    );
  }

  if (!destination) {
    return (
      <div className="min-h-screen bg-[#16191D] luxury-scrollbar">
        <Header />
        <main className="pt-16">
          <div className="container mx-auto px-6 py-32 text-center">
            <div className="max-w-2xl mx-auto">
              {/* Elegant Error Icon */}
              <div className="w-24 h-24 bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20 rounded-full flex items-center justify-center mx-auto mb-8">
                <MapPin className="h-12 w-12 text-[#D4C2A4]" />
              </div>

              {/* Luxury Error Message */}
              <h1 className="font-['Cormorant_Garamond'] text-4xl md:text-5xl font-light text-[#F2EEE6] mb-6">
                Destination Not Found
              </h1>
              <p className="font-['Open_Sans'] text-lg text-[#F2EEE6]/80 mb-12 leading-relaxed">
                The destination you're seeking appears to have wandered off the beaten path.
                Let us guide you back to our collection of extraordinary safari experiences.
              </p>

              {/* Luxury Back Button */}
              <button className="group inline-flex items-center gap-4 px-10 py-4 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-sm tracking-wide uppercase rounded-sm transition-all duration-500 hover:bg-[#F2EEE6] hover:shadow-2xl hover:shadow-[#D4C2A4]/20 transform hover:-translate-y-1">
                <Link to="/destinations" className="flex items-center gap-4">
                  <ArrowLeft className="h-4 w-4 group-hover:-translate-x-1 transition-transform duration-300" />
                  Return to Destinations
                </Link>
              </button>
            </div>
          </div>
        </main>
        <Footer isDarkBackground={true} />
      </div>
    );
  }

  const defaultImages = [
    'photo-1472396961693-142e6e269027',
    'photo-1466721591366-2d5fba72006d',
    'photo-1547036967-23d11aacaee0',
    'photo-1493962853295-0fd70327578a'
  ];

  const images = destination.images && destination.images.length > 0 ? destination.images : defaultImages;

  return (
    <div className="min-h-screen bg-[#16191D] luxury-scrollbar">
      <Header />
      <main className="">
        {/* Luxury Hero Section - Mobile Responsive */}
        <div className="relative h-screen sm:h-screen overflow-hidden">
          {/* Background Image with Parallax Effect */}
          <div className="absolute inset-0 w-full h-full">
            <img
              src={`https://images.unsplash.com/${images[selectedImageIndex]}?auto=format&fit=crop&w=1920&h=1080`}
              alt={destination.name}
              className="w-full h-full object-cover scale-110 transition-transform duration-700"
            />
            {/* Luxury Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-b from-[#16191D]/60 via-[#16191D]/40 to-[#16191D]/90"></div>
          </div>

          {/* Elegant Navigation - Mobile Responsive */}
          <div className="absolute top-4 sm:top-8 left-4 sm:left-8 pt-16 z-20">
            <button className="group inline-flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20 text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm font-medium rounded-sm transition-all duration-300 hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/40">
              <Link to="/destinations" className="flex items-center gap-2 sm:gap-3">
                <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 group-hover:-translate-x-1 transition-transform duration-300" />
                <span className="hidden sm:inline">Back to Destinations</span>
                <span className="sm:hidden">Back</span>
              </Link>
            </button>
          </div>

          {/* Luxury Content Container - Mobile Responsive */}
          <div className="absolute inset-0 flex items-center justify-center z-10 px-4 sm:px-6">
            <div className="text-center max-w-6xl mx-auto w-full">
              {/* Premium Badge - Mobile Responsive */}
              {destination.featured && (
                <div className="inline-flex items-center gap-2 sm:gap-3 mb-6 sm:mb-8 px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20">
                  <Star className="w-4 h-4 sm:w-5 sm:h-5 text-[#D4C2A4]" />
                  <span className="text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm tracking-wider uppercase">
                    Featured Destination
                  </span>
                </div>
              )}

              {/* Elegant Title - Mobile Responsive */}
              <h1 className="font-['Cormorant_Garamond'] text-3xl sm:text-5xl md:text-7xl lg:text-8xl xl:text-9xl font-light leading-tight sm:leading-none mb-6 sm:mb-8 text-[#F2EEE6] px-2">
                {destination.name}
              </h1>

              {/* Sophisticated Location - Mobile Responsive */}
              <div className="flex items-center justify-center gap-2 sm:gap-3 mb-8 sm:mb-12">
                <div className="w-8 h-8 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                  <MapPin className="h-4 w-4 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                </div>
                <p className="font-['Open_Sans'] text-sm sm:text-xl md:text-2xl text-[#F2EEE6]/90">
                  {destination.region}, {destination.country}
                </p>
              </div>

              {/* Luxury Description - Mobile Responsive */}
              <p className="font-['Open_Sans'] text-sm sm:text-lg md:text-xl leading-relaxed text-[#F2EEE6]/80 max-w-4xl mx-auto mb-10 sm:mb-16 px-4">
                {destination.description}
              </p>

              {/* Luxury Action Buttons - Mobile Responsive */}
              <div className="flex flex-col gap-4 sm:gap-6 justify-center px-4">
                <button className="group px-6 sm:px-10 py-3 sm:py-4 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-xs sm:text-sm tracking-wide uppercase rounded-sm transition-all duration-500 hover:bg-[#F2EEE6] hover:shadow-2xl hover:shadow-[#D4C2A4]/20 transform hover:-translate-y-1">
                  <span className="flex items-center justify-center gap-2 sm:gap-3">
                    Plan Your Safari
                    <Plane className="w-3 h-3 sm:w-4 sm:h-4 group-hover:rotate-12 transition-transform duration-300" />
                  </span>
                </button>
                <button className="group px-6 sm:px-10 py-3 sm:py-4 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transform hover:-translate-y-1">
                  <span className="flex items-center justify-center gap-2 sm:gap-3">
                    View Gallery
                    <Camera className="w-3 h-3 sm:w-4 sm:h-4 group-hover:scale-110 transition-transform duration-300" />
                  </span>
                </button>
              </div>
            </div>
          </div>

          {/* Elegant Image Gallery Thumbnails - Mobile Responsive */}
          {images.length > 1 && (
            <div className="absolute bottom-4 sm:bottom-8 right-4 sm:right-8 z-20">
              {/* Show 3 images on mobile, 4 on larger screens */}
              <div className="flex gap-2 sm:gap-3 sm:hidden">
                {images.slice(0, 3).map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`group w-12 h-10 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                      selectedImageIndex === index
                        ? 'border-[#D4C2A4] shadow-lg shadow-[#D4C2A4]/20'
                        : 'border-[#D4C2A4]/30 hover:border-[#D4C2A4]/60'
                    }`}
                  >
                    <img
                      src={`https://images.unsplash.com/${image}?auto=format&fit=crop&w=200&h=150`}
                      alt={`${destination.name} view ${index + 1}`}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                  </button>
                ))}
              </div>
              <div className="hidden sm:flex gap-2 sm:gap-3">
                {images.slice(0, 4).map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`group w-12 h-10 sm:w-20 sm:h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                      selectedImageIndex === index
                        ? 'border-[#D4C2A4] shadow-lg shadow-[#D4C2A4]/20'
                        : 'border-[#D4C2A4]/30 hover:border-[#D4C2A4]/60'
                    }`}
                  >
                    <img
                      src={`https://images.unsplash.com/${image}?auto=format&fit=crop&w=200&h=150`}
                      alt={`${destination.name} view ${index + 1}`}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Elegant Scroll Indicator - Mobile Responsive */}
          <div className="absolute bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-20">
            <div className="w-5 h-8 sm:w-6 sm:h-10 border-2 border-[#D4C2A4]/50 rounded-full flex justify-center">
              <div className="w-1 h-2 sm:h-3 bg-[#D4C2A4] rounded-full mt-1 sm:mt-2 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Luxury Content Section - Mobile Responsive */}
        <div className="relative bg-[#16191D] py-12 sm:py-20 md:py-32">
          {/* Elegant Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
              backgroundSize: '50px 50px'
            }}></div>
          </div>

          <div className="container mx-auto px-4 sm:px-6 relative z-10">
            <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 sm:gap-8 lg:gap-12">
              {/* Luxury Main Content - Mobile Responsive */}
              <div className="xl:col-span-3">
                <Tabs defaultValue="overview" className="w-full">
                  {/* Luxury Tab Navigation - Mobile Responsive */}
                  <div className="flex justify-center mb-8 sm:mb-12 lg:mb-16">
                    <div className="inline-flex bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/10 rounded-full p-1 sm:p-2 w-full max-w-full overflow-x-auto">
                      <TabsList className="bg-transparent border-none grid grid-cols-4 gap-1 sm:gap-2 h-auto w-full min-w-max">
                        <TabsTrigger
                          value="overview"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10 whitespace-nowrap"
                        >
                          Overview
                        </TabsTrigger>
                        <TabsTrigger
                          value="wildlife"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10 whitespace-nowrap"
                        >
                          Wildlife
                        </TabsTrigger>
                        <TabsTrigger
                          value="activities"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10 whitespace-nowrap"
                        >
                          Activities
                        </TabsTrigger>
                        <TabsTrigger
                          value="guide"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10 whitespace-nowrap"
                        >
                          <span className="hidden sm:inline">Travel Guide</span>
                          <span className="sm:hidden">Guide</span>
                        </TabsTrigger>
                      </TabsList>
                    </div>
                  </div>

                <TabsContent value="overview" className="space-y-6 sm:space-y-8 lg:space-y-12">
                  {/* Luxury About Section - Mobile Responsive */}
                  <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                    <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                        <Info className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                      </div>
                      <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                        About {(destination as any).name || 'This Destination'}
                      </h3>
                    </div>
                    <div className="space-y-4 sm:space-y-6">
                      <p className="font-['Open_Sans'] text-[#F2EEE6]/90 leading-relaxed text-sm sm:text-base lg:text-lg">
                        {(destination as any).description || 'Discover the extraordinary beauty and wildlife of this remarkable destination.'}
                      </p>
                      {(destination as any).detailedGuide?.overview && (
                        <p className="font-['Open_Sans'] text-[#F2EEE6]/80 leading-relaxed text-sm sm:text-base">
                          {(destination as any).detailedGuide.overview}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Geography Section - Mobile Responsive */}
                  {(destination as any).detailedGuide?.geography && (
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                      <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                          <TreePine className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                        </div>
                        <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                          Geography & Landscape
                        </h3>
                      </div>
                      <p className="font-['Open_Sans'] text-[#F2EEE6]/90 leading-relaxed text-sm sm:text-base lg:text-lg">
                        {(destination as any).detailedGuide.geography}
                      </p>
                    </div>
                  )}

                  {/* History Section - Mobile Responsive */}
                  {(destination as any).detailedGuide?.history && (
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                      <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                          <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                        </div>
                        <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                          Historical Background
                        </h3>
                      </div>
                      <p className="font-['Open_Sans'] text-[#F2EEE6]/90 leading-relaxed text-sm sm:text-base lg:text-lg">
                        {(destination as any).detailedGuide.history}
                      </p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="wildlife" className="space-y-6 sm:space-y-8 lg:space-y-12">
                  {/* Luxury Wildlife Section - Mobile Responsive */}
                  <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                    <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                        <Camera className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                      </div>
                      <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                        Wildlife Species ({(destination as any).wildlife?.length || 0})
                      </h3>
                    </div>

                    {(destination as any).wildlife && (destination as any).wildlife.length > 0 ? (
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                        {(destination as any).wildlife.map((animal: any, index: number) => (
                          <div key={index} className="group bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/10 rounded-lg p-4 sm:p-6 transition-all duration-300 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
                            {/* Animal Header - Mobile Responsive */}
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 sm:gap-4 mb-3 sm:mb-4">
                              <h4 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6]">
                                {animal.species}
                              </h4>
                              <span className="px-2 sm:px-3 py-1 bg-[#D4C2A4]/20 text-[#D4C2A4] font-['Open_Sans'] text-xs font-medium rounded-full self-start">
                                {animal.abundance}
                              </span>
                            </div>

                            {/* Scientific Name - Mobile Responsive */}
                            <p className="font-['Open_Sans'] text-xs sm:text-sm text-[#D4C2A4] italic mb-2 sm:mb-3">
                              {animal.scientificName}
                            </p>

                            {/* Behavior - Mobile Responsive */}
                            <p className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed mb-3 sm:mb-4">
                              {animal.behavior}
                            </p>

                            {/* Details - Mobile Responsive */}
                            <div className="space-y-2 sm:space-y-3">
                              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                                <span className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs">Best Spotting Time</span>
                                <span className="font-['Open_Sans'] text-[#F2EEE6] text-xs">{animal.bestSpottingTime}</span>
                              </div>
                              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                                <span className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs">Conservation Status</span>
                                <span className={`px-2 py-1 rounded-full text-xs font-['Open_Sans'] font-medium self-start sm:self-auto ${
                                  animal.conservationStatus === 'Endangered'
                                    ? 'bg-red-500/20 text-red-400'
                                    : 'bg-green-500/20 text-green-400'
                                }`}>
                                  {animal.conservationStatus}
                                </span>
                              </div>

                              {/* Photography Tips - Mobile Responsive */}
                              {animal.photographyTips && (
                                <div className="pt-2 sm:pt-3 border-t border-[#D4C2A4]/10">
                                  <div className="flex items-start gap-2">
                                    <Camera className="h-3 w-3 sm:h-4 sm:w-4 text-[#D4C2A4] mt-0.5 flex-shrink-0" />
                                    <p className="font-['Open_Sans'] text-[#D4C2A4] text-xs leading-relaxed">
                                      {animal.photographyTips}
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 sm:py-12">
                        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#D4C2A4]/10 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                          <Camera className="h-6 w-6 sm:h-8 sm:w-8 text-[#D4C2A4]/50" />
                        </div>
                        <p className="font-['Open_Sans'] text-[#F2EEE6]/60 text-sm sm:text-base">Wildlife information will be available soon.</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="activities" className="space-y-6 sm:space-y-8 lg:space-y-12">
                  {/* Luxury Activities Section - Mobile Responsive */}
                  <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                    <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                        <Compass className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                      </div>
                      <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                        Available Activities
                      </h3>
                    </div>

                    {(destination as any).activities && (destination as any).activities.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                        {(destination as any).activities.map((activity: string, index: number) => (
                          <div key={index} className="group bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/10 rounded-lg p-3 sm:p-4 text-center transition-all duration-300 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30 hover:shadow-lg hover:shadow-[#D4C2A4]/10">
                            <span className="font-['Open_Sans'] text-[#F2EEE6] text-xs sm:text-sm font-medium group-hover:text-[#D4C2A4] transition-colors duration-300">
                              {activity}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 sm:py-12">
                        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#D4C2A4]/10 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                          <Compass className="h-6 w-6 sm:h-8 sm:w-8 text-[#D4C2A4]/50" />
                        </div>
                        <p className="font-['Open_Sans'] text-[#F2EEE6]/60 text-sm sm:text-base">Activity information will be available soon.</p>
                      </div>
                    )}
                  </div>

                  {/* Luxury Accommodations Section - Mobile Responsive */}
                  {(destination as any).accommodations && (destination as any).accommodations.length > 0 && (
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                      <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                          <Award className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                        </div>
                        <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                          Luxury Accommodations
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                        {(destination as any).accommodations.map((accommodation: string, index: number) => (
                          <div key={index} className="group bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/10 rounded-lg p-4 sm:p-6 transition-all duration-300 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
                            <div className="flex items-center gap-2 sm:gap-3">
                              <div className="w-2 h-2 bg-[#D4C2A4] rounded-full flex-shrink-0"></div>
                              <p className="font-['Open_Sans'] text-[#F2EEE6] font-medium text-sm sm:text-base">{accommodation}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="guide" className="space-y-6 sm:space-y-8 lg:space-y-12">
                  {/* Best Time to Visit - Mobile Responsive */}
                  {(destination as any).detailedGuide?.bestTimeToVisit && (
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                      <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                          <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                        </div>
                        <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                          Best Time to Visit
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                        {(destination as any).detailedGuide.bestTimeToVisit.drySeason && (
                          <div className="bg-[#D4C2A4]/5 rounded-lg p-4 sm:p-6 border border-[#D4C2A4]/10">
                            <h4 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-blue-400 mb-3 sm:mb-4">Dry Season</h4>
                            <p className="font-['Open_Sans'] text-[#F2EEE6]/90 leading-relaxed text-sm sm:text-base">
                              {(destination as any).detailedGuide.bestTimeToVisit.drySeason}
                            </p>
                          </div>
                        )}
                        {(destination as any).detailedGuide.bestTimeToVisit.greenSeason && (
                          <div className="bg-[#D4C2A4]/5 rounded-lg p-4 sm:p-6 border border-[#D4C2A4]/10">
                            <h4 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-green-400 mb-3 sm:mb-4">Green Season</h4>
                            <p className="font-['Open_Sans'] text-[#F2EEE6]/90 leading-relaxed text-sm sm:text-base">
                              {(destination as any).detailedGuide.bestTimeToVisit.greenSeason}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Packing Tips - Mobile Responsive */}
                  {(destination as any).detailedGuide?.packingTips && (destination as any).detailedGuide.packingTips.length > 0 && (
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                      <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                          <Heart className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                        </div>
                        <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                          Essential Packing Tips
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                        {(destination as any).detailedGuide.packingTips.map((tip: string, index: number) => (
                          <div key={index} className="flex items-start gap-2 sm:gap-3">
                            <div className="w-2 h-2 bg-green-400 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></div>
                            <span className="font-['Open_Sans'] text-[#F2EEE6]/90 text-xs sm:text-sm leading-relaxed">{tip}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Travel Tips - Mobile Responsive */}
                  {(destination as any).detailedGuide?.travelTips && (destination as any).detailedGuide.travelTips.length > 0 && (
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                      <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                          <Globe className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                        </div>
                        <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                          Expert Travel Tips
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                        {(destination as any).detailedGuide.travelTips.map((tip: string, index: number) => (
                          <div key={index} className="flex items-start gap-2 sm:gap-3">
                            <div className="w-2 h-2 bg-[#D4C2A4] rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></div>
                            <span className="font-['Open_Sans'] text-[#F2EEE6]/90 text-xs sm:text-sm leading-relaxed">{tip}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
              </div>

              {/* Luxury Sidebar - Mobile Responsive */}
              <div className="space-y-6 sm:space-y-8">
                {/* Quick Information - Mobile Responsive */}
                <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6">
                  <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6] mb-4 sm:mb-6">
                    Quick Information
                  </h3>
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-2 sm:py-3 border-b border-[#D4C2A4]/10 gap-1 sm:gap-0">
                      <span className="flex items-center font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">
                        <Thermometer className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                        Climate
                      </span>
                      <span className="font-['Open_Sans'] text-[#F2EEE6] text-xs sm:text-sm font-medium">
                        {(destination as any).climate || 'Tropical'}
                      </span>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-2 sm:py-3 border-b border-[#D4C2A4]/10 gap-1 sm:gap-0">
                      <span className="flex items-center font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">
                        <Clock className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                        Best Months
                      </span>
                      <span className="font-['Open_Sans'] text-[#F2EEE6] text-xs sm:text-sm font-medium">
                        {(destination as any).bestTimeToVisit?.join(', ') || 'Year Round'}
                      </span>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-2 sm:py-3 border-b border-[#D4C2A4]/10 gap-1 sm:gap-0">
                      <span className="flex items-center font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">
                        <Camera className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                        Wildlife Species
                      </span>
                      <span className="font-['Open_Sans'] text-[#D4C2A4] text-xs sm:text-sm font-semibold">
                        {(destination as any).wildlife?.length || 0}
                      </span>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-2 sm:py-3 gap-1 sm:gap-0">
                      <span className="flex items-center font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">
                        <Users className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                        Activities
                      </span>
                      <span className="font-['Open_Sans'] text-[#D4C2A4] text-xs sm:text-sm font-semibold">
                        {(destination as any).activities?.length || 0}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Luxury Action Buttons - Mobile Responsive */}
                <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6">
                  <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6] mb-4 sm:mb-6">
                    Plan Your Visit
                  </h3>
                  <div className="space-y-3 sm:space-y-4">
                    <button className="w-full group px-4 sm:px-6 py-2.5 sm:py-3 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-xs sm:text-sm tracking-wide rounded-sm transition-all duration-300 hover:bg-[#F2EEE6] hover:shadow-lg hover:shadow-[#D4C2A4]/20">
                      <Link to="/tours" className="flex items-center justify-center gap-2 sm:gap-3">
                        <Plane className="h-3 w-3 sm:h-4 sm:w-4 group-hover:rotate-12 transition-transform duration-300" />
                        View Safari Tours
                      </Link>
                    </button>

                    <button className="w-full group px-4 sm:px-6 py-2.5 sm:py-3 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide rounded-sm transition-all duration-300 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]">
                      <Link to="/tour-builder" className="flex items-center justify-center gap-2 sm:gap-3">
                        <Heart className="h-3 w-3 sm:h-4 sm:w-4 group-hover:scale-110 transition-transform duration-300" />
                        Custom Tour Builder
                      </Link>
                    </button>

                    <button className="w-full group px-4 sm:px-6 py-2.5 sm:py-3 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide rounded-sm transition-all duration-300 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]">
                      <Link to="/contact" className="flex items-center justify-center gap-2 sm:gap-3">
                        <Users className="h-3 w-3 sm:h-4 sm:w-4 group-hover:scale-110 transition-transform duration-300" />
                        Contact Expert
                      </Link>
                    </button>
                  </div>
                </div>

                {/* Conservation Information - Mobile Responsive */}
                {(destination as any).conservationInfo && (
                  <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6">
                    <div className="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center">
                        <Shield className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                      </div>
                      <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6]">
                        Conservation
                      </h3>
                    </div>

                    {(destination as any).conservationInfo.conservationFee && (
                      <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-[#D4C2A4]/5 rounded-lg border border-[#D4C2A4]/10">
                        <p className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm mb-1">Conservation Fee</p>
                        <p className="font-['Cormorant_Garamond'] text-xl sm:text-2xl font-semibold text-[#D4C2A4]">
                          ${(destination as any).conservationInfo.conservationFee}
                        </p>
                      </div>
                    )}

                    {(destination as any).conservationInfo.howTouristsHelp && (destination as any).conservationInfo.howTouristsHelp.length > 0 && (
                      <div>
                        <p className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm mb-3 sm:mb-4">How You Can Help</p>
                        <div className="space-y-2 sm:space-y-3">
                          {(destination as any).conservationInfo.howTouristsHelp.slice(0, 3).map((help: string, index: number) => (
                            <div key={index} className="flex items-start gap-2 sm:gap-3">
                              <div className="w-2 h-2 bg-green-400 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></div>
                              <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs leading-relaxed">{help}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        {/* Luxury Footer Transition - Mobile Responsive */}
        <div className="relative bg-[#16191D] py-10 sm:py-12 lg:py-16">
          <div className="container mx-auto px-4 sm:px-6 text-center">
            {/* Elegant Divider - Mobile Responsive */}
            <div className="flex items-center justify-center mb-8 sm:mb-10 lg:mb-12">
              <div className="w-16 sm:w-24 lg:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
              <div className="mx-3 sm:mx-4 w-2 h-2 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full"></div>
              <div className="w-16 sm:w-24 lg:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
            </div>

            {/* Luxury CTA - Mobile Responsive */}
            <h2 className="font-['Cormorant_Garamond'] text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-light text-[#F2EEE6] mb-4 sm:mb-6 px-4">
              Ready to Experience
              <span className="block text-[#D4C2A4] italic">{(destination as any).name || 'This Destination'}?</span>
            </h2>

            <p className="font-['Open_Sans'] text-sm sm:text-base lg:text-lg text-[#F2EEE6]/80 mb-8 sm:mb-10 max-w-2xl mx-auto px-4 leading-relaxed">
              Let our expert guides craft your perfect safari experience,
              tailored to showcase the very best of this remarkable destination.
            </p>

            <div className="flex flex-col gap-4 sm:gap-6 justify-center px-4">
              <button className="px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-xs sm:text-sm tracking-wide uppercase rounded-sm transition-all duration-500 hover:bg-[#F2EEE6] hover:shadow-2xl hover:shadow-[#D4C2A4]/20 transform hover:-translate-y-1">
                Plan Your Safari
              </button>
              <button className="px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transform hover:-translate-y-1">
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default DestinationDetail;
