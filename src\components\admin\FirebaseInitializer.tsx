
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { initializeFirebaseCollections, createAdminUser } from '@/utils/initializeFirebase';

const FirebaseInitializer = () => {
  const [loading, setLoading] = useState(false);
  const [adminEmail, setAdminEmail] = useState('<EMAIL>');
  const [adminPassword, setAdminPassword] = useState('SafariAdmin2024!');
  const [adminName, setAdminName] = useState('Safari Admin');
  const { toast } = useToast();

  const handleInitializeCollections = async () => {
    setLoading(true);
    try {
      await initializeFirebaseCollections();
      toast({
        title: "Success!",
        description: "Firebase collections have been initialized with sample data.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to initialize Firebase collections. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAdmin = async () => {
    setLoading(true);
    try {
      await createAdminUser(adminEmail, adminPassword, adminName);
      toast({
        title: "Success!",
        description: "Admin user created successfully. You can now log in with these credentials.",
      });
    } catch (error: any) {
      let errorMessage = "Failed to create admin user.";
      if (error.code === 'auth/email-already-in-use') {
        errorMessage = "This email is already registered.";
      } else if (error.code === 'auth/weak-password') {
        errorMessage = "Password should be at least 6 characters.";
      }
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Firebase Setup</CardTitle>
          <CardDescription>
            Initialize your Firebase database with sample data and create an admin user.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Initialize Collections */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">1. Initialize Database Collections</h3>
            <p className="text-sm text-gray-600">
              This will create all necessary Firestore collections with sample data including tours, destinations, accommodations, and blog posts.
            </p>
            <Button 
              onClick={handleInitializeCollections} 
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Initializing...' : 'Initialize Collections'}
            </Button>
          </div>

          {/* Create Admin User */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">2. Create Admin User</h3>
            <div className="space-y-3">
              <div>
                <Label htmlFor="email">Admin Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={adminEmail}
                  onChange={(e) => setAdminEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="password">Admin Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={adminPassword}
                  onChange={(e) => setAdminPassword(e.target.value)}
                  placeholder="Enter a strong password"
                />
              </div>
              <div>
                <Label htmlFor="name">Display Name</Label>
                <Input
                  id="name"
                  type="text"
                  value={adminName}
                  onChange={(e) => setAdminName(e.target.value)}
                  placeholder="Safari Admin"
                />
              </div>
            </div>
            <Button 
              onClick={handleCreateAdmin} 
              disabled={loading || !adminEmail || !adminPassword || !adminName}
              className="w-full"
            >
              {loading ? 'Creating Admin...' : 'Create Admin User'}
            </Button>
          </div>

          <div className="text-sm text-gray-500 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold mb-2">What this will create:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>Tours collection with sample safari tours</li>
              <li>Destinations collection with park information</li>
              <li>Accommodations collection with lodges and camps</li>
              <li>Blog posts collection with sample articles</li>
              <li>Empty collections for bookings, reviews, etc.</li>
              <li>Admin user with full access privileges</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FirebaseInitializer;
