import { Timestamp } from 'firebase/firestore';

export interface SearchFilters {
  destination?: string;
  duration?: string;
  priceRange?: { min: number; max: number };
  difficulty?: 'easy' | 'moderate' | 'challenging';
  tourType?: 'standard' | 'luxury' | 'budget';
  category?: string;
  maxGroupSize?: number;
  activities?: string[];
  dateRange?: { start: Date; end: Date };
  price?: { min: number; max: number };
  photography?: boolean;
  birding?: boolean;
  conservation?: boolean;
  cultural?: boolean;
  wildlife?: string[];
  season?: 'dry' | 'green' | 'year-round';
}

export interface TourAvailability {
  date: string;
  available: boolean;
  spots: number;
  price: number;
  spotsRemaining?: number;
  priceModifier?: number;
  season?: string;
  wildlifeActivity?: string;
  weatherConditions?: string;
}

export interface TourPackage {
  id: string;
  name: string;
  title?: string;
  description: string;
  price: number;
  totalPrice?: number;
  discount?: number;
  inclusions: string[];
  includes?: string[];
  duration: string;
  totalDuration?: string;
  featured?: boolean;
  itinerary?: Array<{
    day: number;
    title: string;
    description: string;
  }>;
}

export interface WildlifeInfo {
  species: string;
  scientificName: string;
  category: string;
  abundance: string;
  bestSpottingTime: string;
  behavior: string;
  conservationStatus: string;
  photographyTips: string;
}

export interface Tour {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  location: string;
  destinations: string[];
  activities: string[];
  accommodations: string[];
  maxGroupSize: number;
  minGroupSize: number;
  difficulty: 'easy' | 'moderate' | 'challenging';
  includes: string[];
  excludes: string[];
  images: string[];
  featured: boolean;
  status: 'active' | 'inactive' | 'draft';
  rating: number;
  reviewCount: number;
  tourType: 'standard' | 'luxury' | 'budget';
  category?: string;
  accommodationLevel?: string;
  seasonality: {
    greenSeason: boolean;
    drySeason: boolean;
    bestMonths: string[];
  };
  routeMap?: {
    startPoint: { lat: number; lng: number; name: string };
    endPoint: { lat: number; lng: number; name: string };
    waypoints: Array<{
      lat: number;
      lng: number;
      name: string;
      description?: string;
      order: number;
    }>;
  };
  itinerary: Array<{
    day: number;
    title: string;
    description: string;
    accommodation: string;
    meals: string[];
    activities: Array<{
      time: string;
      activity: string;
      description: string;
      duration: string;
      location: string;
    }>;
    drivingTime: string;
    highlights: string[];
  }>;
  fitnessRequirements: {
    level: string;
    description: string;
    walkingDistance: string;
    terrain: string;
    ageRestrictions: string;
    medicalConditions: string[];
  };
  equipment: {
    provided: Array<{
      name: string;
      description: string;
      category: string;
      optional: boolean;
    }>;
    recommended: Array<{
      name: string;
      description: string;
      category: string;
      optional: boolean;
    }>;
    required: Array<{
      name: string;
      description: string;
      category: string;
      optional: boolean;
    }>;
  };
  groupOptions: Array<{
    type: string;
    minParticipants: number;
    maxParticipants: number;
    pricePerPerson: number;
    description: string;
  }>;
  specialFeatures: string[];
  difficultyDetails: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Destination {
  id: string;
  name: string;
  description: string;
  country: string;
  region: string;
  coordinates: { lat: number; lng: number };
  bestTimeToVisit: string[];
  climate: string;
  wildlife: Array<{
    species: string;
    scientificName: string;
    category: string;
    abundance: string;
    bestSpottingTime: string;
    behavior: string;
    conservationStatus: string;
    photographyTips: string;
  }>;
  images: string[];
  activities: string[];
  accommodations: string[];
  featured: boolean;
  detailedGuide: {
    overview: string;
    geography: string;
    history: string;
    bestTimeToVisit: {
      drySeason: string;
      greenSeason: string;
      photography: string;
      birding: string;
    };
    gettingThere: string;
    accommodation: string;
    packingTips: string[];
    healthSafety: string;
    travelTips: string[];
  };
  seasonalInfo: {
    drySeason: {
      months: string[];
      description: string;
      wildlife: string;
      photography: string;
      advantages: string[];
      disadvantages: string[];
    };
    greenSeason: {
      months: string[];
      description: string;
      wildlife: string;
      photography: string;
      advantages: string[];
      disadvantages: string[];
    };
  };
  conservationInfo: {
    initiatives: string[];
    challenges: string[];
    howTouristsHelp: string[];
    conservationFee: number;
  };
  culturalInfo: {
    tribes: string[];
    languages: string[];
    traditions: string[];
    etiquette: string[];
    culturalSites: string[];
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Booking {
  id: string;
  userId: string;
  tourId: string;
  tourTitle: string;
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    country: string;
    passportNumber?: string;
    emergencyContact: {
      name: string;
      phone: string;
      relationship: string;
    };
  };
  bookingDetails: {
    startDate: string;
    endDate: string;
    participants: number;
    childrenCount: number;
    accommodationType: string;
    specialRequests?: string;
    dietaryRestrictions: string[];
  };
  pricing: {
    basePrice: number;
    participantCount: number;
    subtotal: number;
    taxes: number;
    totalAmount: number;
    currency: string;
  };
  paymentInfo: {
    paymentMethod: string;
    paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
    transactionId?: string;
    amountPaid: number;
    paymentDate?: Timestamp;
  };
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface UserProfile {
  id?: string;
  uid: string;
  email: string;
  displayName: string;
  phone?: string;
  country?: string;
  profileImage?: string;
  role: 'user' | 'admin' | 'guide';
  preferences: {
    accommodation: 'budget' | 'midrange' | 'luxury';
    activities: string[];
    dietaryRestrictions: string[];
    fitnessLevel: 'low' | 'moderate' | 'high';
    photographyInterest: boolean;
    birdingInterest: boolean;
  };
  loyaltyPoints: number;
  pastBookings: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Review {
  id: string;
  tourId: string;
  tourName: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  content: string;
  images?: string[];
  verified: boolean;
  helpful: number;
  response?: {
    author: string;
    content: string;
    date: string;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ContactMessage {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  status: 'unread' | 'read' | 'replied';
  priority: 'low' | 'medium' | 'high';
  category: 'general' | 'booking' | 'complaint' | 'suggestion';
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'booking' | 'payment' | 'reminder' | 'promotion' | 'system';
  read: boolean;
  actionUrl?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface GalleryImage {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  url?: string; // For frontend compatibility
  category: string;
  photographer: string;
  location: string;
  dateTaken: string;
  tags: string[];
  featured: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string;
  authorId: string;
  authorName: string;
  category: string;
  tags: string[];
  published: boolean;
  publishedAt: Timestamp;
  viewCount: number;
  wildlifeSpotted: string[];
  photographyTips: string[];
  conservationMessage: string;
  culturalInsights: string[];
  seasonalRelevance: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Activity {
  id: string;
  name: string;
  description: string;
  category: string;
  duration: string;
  difficulty: string;
  equipment: string[];
  price: number;
  images: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Guide {
  id: string;
  name: string;
  bio: string;
  experience: string;
  languages: string[];
  specialties: string[];
  image: string;
  rating: number;
  reviewCount: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface TravelGuide {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  images: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface PackingList {
  id: string;
  title: string;
  description: string;
  category: string;
  items: Array<{
    name: string;
    essential: boolean;
    notes?: string;
  }>;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface WildlifeSighting {
  id: string;
  species: string;
  location: string;
  date: Timestamp;
  guide: string;
  description: string;
  images: string[];
  createdAt: Timestamp;
}

export interface CustomTourRequest {
  id: string;
  // Basic Info
  duration: number;
  participants: number;
  budget: number[];
  startDate: string;

  // Destinations
  destinations: string[];

  // Interests
  interests: string[];

  // Accommodation
  accommodation: 'budget' | 'midrange' | 'luxury';

  // Activities
  activities: string[];

  // Special Requirements
  specialRequests: string;
  fitnessLevel: 'easy' | 'moderate' | 'challenging';
  photographyInterest: boolean;

  // Contact Info
  name: string;
  email: string;
  phone: string;

  // Status
  status: 'pending' | 'reviewed' | 'quoted' | 'confirmed' | 'cancelled';
  adminNotes?: string;
  quotedPrice?: number;

  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Accommodation {
  id: string;
  name: string;
  description: string;
  type: 'lodge' | 'camp' | 'hotel' | 'guesthouse';
  category: 'budget' | 'midrange' | 'luxury';
  location: string;
  coordinates: { lat: number; lng: number };
  amenities: string[];
  roomTypes: Array<{
    name: string;
    description: string;
    maxOccupancy: number;
    price: number;
    amenities: string[];
    images: string[];
    wildlifeViewing: boolean;
  }>;
  images: string[];
  rating: number;
  reviewCount: number;
  pricePerNight: number;
  maxGuests: number;
  sustainability: {
    ecoFriendly: boolean;
    localCommunitySupport: boolean;
    conservationEfforts: string[];
    sustainablePractices: string[];
    certifications: string[];
  };
  wildlifeViewing: boolean;
  photographyFacilities: string[];
  conservationPrograms: string[];
  culturalExperiences: string[];
  accessibility: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Enhanced Search Types
export interface SearchResult {
  id: string;
  title: string;
  type: 'tour' | 'destination';
  description: string;
  image: string;
  location: string;
  rating?: number;
  // Tour-specific fields
  price?: number;
  duration?: string;
  difficulty?: 'easy' | 'moderate' | 'challenging';
  maxGroupSize?: number;
  // Destination-specific fields
  country?: string;
  region?: string;
  bestTimeToVisit?: string[];
  activities?: string[];
}

export interface EnhancedSearchFilters {
  type?: 'tour' | 'destination' | 'all';
  priceRange?: [number, number];
  duration?: string;
  difficulty?: 'easy' | 'moderate' | 'challenging';
  destination?: string;
  category?: string;
}

export interface SearchState {
  query: string;
  results: SearchResult[];
  loading: boolean;
  error: string | null;
  showResults: boolean;
  filters: EnhancedSearchFilters;
}
