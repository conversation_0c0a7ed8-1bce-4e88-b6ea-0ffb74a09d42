

import React, { useState, useLayoutEffect, useRef, useEffect, useCallback } from 'react';
import { gsap } from 'gsap';
import { Flip } from 'gsap/Flip';
import { CustomEase } from 'gsap/CustomEase';
import '../../styles/monochrome-gallery-luxury.css';

// Register GSAP plugins
gsap.registerPlugin(Flip, CustomEase);





const TOURISM_EXPERIENCES = [
  {
    imgUrl: "https://images.unsplash.com/photo-1634742193353-49a7d1667e1c?w=800&h=600&fit=crop&crop=center",
    title: "MOUNTAIN CLIMBING",
    paragraph: "Conquer Africa's highest peaks with expert guides. Experience the thrill of scaling Mount Kilimanjaro and witness breathtaking sunrise views from the roof of Africa. Our climbing expeditions combine adventure with safety, ensuring an unforgettable journey to the summit."
  },
  {
    imgUrl: "https://images.unsplash.com/photo-1627237658972-576d623ef3cf?w=800&h=600&fit=crop&crop=center",
    title: "SWIMMING SAFARIS",
    paragraph: "Dive into crystal-clear waters and discover Tanzania's hidden aquatic treasures. From swimming with dolphins in Zanzibar to exploring pristine coral reefs, our water adventures offer a refreshing perspective on African wildlife and marine ecosystems."
  },
  {
    imgUrl: "https://images.unsplash.com/photo-1659221876406-31a3746f41b9?w=800&h=600&fit=crop&crop=center",
    title: "SKYDIVING ",
    paragraph: "Experience the ultimate adrenaline rush with tandem skydiving over the Serengeti. Witness the Great Migration from above and feel the freedom of flight as you soar through African skies with certified instructors ensuring your safety throughout this once-in-a-lifetime experience."
  },
  {
    imgUrl: "https://images.unsplash.com/photo-1646345098774-acb16125b6f3?w=800&h=600&fit=crop&crop=center",
    title: "WILDLIFE SAFARIS",
    paragraph: "Embark on classic game drives through Tanzania's most renowned national parks. Witness the Big Five in their natural habitat, experience the Great Migration, and create memories that will last a lifetime with our expert guides and luxury safari vehicles."
  },
  {
    imgUrl: "https://images.unsplash.com/photo-1682681611078-2ba8a6f6e0fa?w=800&h=600&fit=crop&crop=center",
    title: "CULTURAL IMMERSION",
    paragraph: "Connect with local Maasai communities and experience authentic African culture. Learn traditional dances, participate in village life, and gain insights into ancient customs and traditions that have been preserved for generations in the heart of Tanzania."
  },
  {
    imgUrl: "https://images.unsplash.com/photo-1725744810970-eae47b4e94c0?w=800&h=600&fit=crop&crop=center",
    title: "HOT AIR BALLOONING",
    paragraph: "Float silently over the endless plains of the Serengeti at sunrise. Experience the magic of African wildlife from a bird's eye view as you drift peacefully above herds of wildebeest, zebras, and elephants in their natural habitat."
  }
];

const TIMING = {
    BASE: 0.512, SHORTEST: 0.256, SHORT: 0.384, LONG: 0.768,
    LONGEST: 1.024, STAGGER_TINY: 0.032, STAGGER_SMALL: 0.064,
    STAGGER_MED: 0.128, PAUSE: 1.536
};

// --- Component ---


const MonochromeGallery: React.FC = () => {
    // --- State Management ---
    const [isAppReady, setAppReady] = useState(false);
    const [preloaderCount, setPreloaderCount] = useState(0);
    const [currentMode, setCurrentMode] = useState<'grid' | 'slider'>('grid');
    const [isAnimating, setIsAnimating] = useState(false);
    const [activeIndex, setActiveIndex] = useState(4);
   
    // --- Refs for DOM elements ---
    const preloaderRef = useRef<HTMLDivElement>(null);
    const gridRef = useRef<HTMLDivElement>(null);
    const gridItemsRef = useRef<(HTMLDivElement | null)[]>([]);
    const sliderImageRef = useRef<HTMLDivElement>(null);
    const sliderImageNextRef = useRef<HTMLDivElement>(null);
    const sliderImageBgRef = useRef<HTMLDivElement>(null);
    const transitionOverlayRef = useRef<HTMLDivElement>(null);
    const contentRef = useRef<HTMLDivElement>(null);
    const contentTitleSpanRef = useRef<HTMLSpanElement>(null);
    const contentParagraphRef = useRef<HTMLDivElement>(null);
    const thumbnailItemsRef = useRef<(HTMLDivElement | null)[]>([]);
    const switchContainerRef = useRef<HTMLDivElement>(null);

    // --- GSAP and Animation Logic (using useLayoutEffect for animations) ---
    useLayoutEffect(() => {
        // Create custom eases once, on mount
        if (typeof CustomEase.get("mainEase") === "undefined") {
            CustomEase.create("mainEase", "M0,0 C0.65,0.05 0.36,1 1,1");
            CustomEase.create("sideEase", "M0,0 C0.86,0 0.07,1 1,1");
        }

        // --- Preloader Animation ---
        const counterInterval = setInterval(() => {
            setPreloaderCount(prev => {
                const newCount = prev + 5;
                if (newCount >= 100) {
                    clearInterval(counterInterval);
                    setTimeout(() => {
                        if (preloaderRef.current) {
                            gsap.to(preloaderRef.current, {
                                opacity: 0,
                                duration: 0.5,
                                onComplete: () => {
                                    setAppReady(true);
                                }
                            });
                        }
                    }, 256);
                    return 100;
                }
                return newCount;
            });
        }, 128);

        return () => clearInterval(counterInterval); // Cleanup on unmount
    }, []);
   
    // --- Core Animation Functions ---
   
    const showSliderView = useCallback(() => {
        if (!gridItemsRef.current[activeIndex] || !sliderImageRef.current || !sliderImageBgRef.current || !contentRef.current || !contentTitleSpanRef.current || !contentParagraphRef.current) return;
       
        setIsAnimating(true);
       
        const activeItem = gridItemsRef.current[activeIndex]!;
        const activeItemRect = activeItem.getBoundingClientRect();
        const activeImageUrl = `url(${TOURISM_EXPERIENCES[activeIndex].imgUrl})`;

        gsap.set([sliderImageRef.current, sliderImageBgRef.current], { backgroundImage: activeImageUrl });
       
        gsap.set(sliderImageRef.current, {
            width: activeItemRect.width, height: activeItemRect.height,
            x: activeItemRect.left, y: activeItemRect.top,
            opacity: 1, visibility: 'visible'
        });

        const tl = gsap.timeline({
            onComplete: () => setIsAnimating(false)
        });

        // Two-step FLIP animation
        const heightState = Flip.getState(sliderImageRef.current);
        gsap.set(sliderImageRef.current, { height: '100%', y: 0 });
        Flip.from(heightState, {
            duration: TIMING.BASE, ease: "mainEase",
            onComplete: () => {
                const widthState = Flip.getState(sliderImageRef.current);
                gsap.set(sliderImageRef.current, { width: '100%', x: 0 });
                Flip.from(widthState, {
                    duration: TIMING.BASE, ease: "mainEase",
                    onComplete: () => {
                        // Fade in content after main animation
                        tl.to(gridRef.current, { opacity: 0, duration: TIMING.SHORTEST, ease: "power2.inOut" }, 0)
                          .to(contentRef.current, { opacity: 1, duration: TIMING.SHORT, ease: "mainEase" }, 0)
                          .to(contentTitleSpanRef.current, { y: 0, duration: TIMING.BASE, ease: "sideEase" }, TIMING.STAGGER_TINY)
                          .to(contentParagraphRef.current, { opacity: 1, duration: TIMING.BASE, ease: "mainEase" }, TIMING.STAGGER_SMALL)
                          .to(thumbnailItemsRef.current, {
                              opacity: 1, y: 0, duration: TIMING.SHORT,
                              stagger: TIMING.STAGGER_TINY, ease: "sideEase"
                          }, TIMING.STAGGER_MED);
                    }
                });
            }
        });
    }, [activeIndex]);

    const showGridView = useCallback(() => {
        if (!gridItemsRef.current[activeIndex] || !sliderImageRef.current) return;

        setIsAnimating(true);
        const activeItem = gridItemsRef.current[activeIndex]!;
        const activeItemRect = activeItem.getBoundingClientRect();
       
        const tl = gsap.timeline({
            onComplete: () => {
                // Main FLIP animation back to grid
                const widthState = Flip.getState(sliderImageRef.current);
                gsap.set(sliderImageRef.current, { width: activeItemRect.width, x: activeItemRect.left });
                Flip.from(widthState, {
                    duration: TIMING.BASE, ease: "mainEase",
                    onComplete: () => {
                        const heightState = Flip.getState(sliderImageRef.current);
                        gsap.set(sliderImageRef.current, { height: activeItemRect.height, y: activeItemRect.top });
                        Flip.from(heightState, {
                            duration: TIMING.BASE, ease: "mainEase",
                            onComplete: () => {
                                gsap.to(sliderImageRef.current, {
                                    opacity: 0, duration: TIMING.SHORTEST, ease: "power2.inOut",
                                    onComplete: () => {
                                        gsap.set(sliderImageRef.current, { visibility: 'hidden' });
                                        setIsAnimating(false);
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });

        // Hide content first
        tl.to(thumbnailItemsRef.current, {
            opacity: 0, y: 20, duration: TIMING.SHORT, stagger: -TIMING.STAGGER_TINY, ease: "sideEase"
          }, 0)
          .to(contentParagraphRef.current, { opacity: 0, duration: TIMING.SHORT, ease: "mainEase" }, TIMING.STAGGER_TINY)
          .to(contentTitleSpanRef.current, { y: "100%", duration: TIMING.SHORT, ease: "sideEase" }, TIMING.STAGGER_SMALL)
          .to(contentRef.current, { opacity: 0, duration: TIMING.SHORT, ease: "mainEase" }, TIMING.STAGGER_MED)
          .to(gridRef.current, { opacity: 1, duration: TIMING.SHORTEST, ease: "power2.inOut" }, ">-0.2");
         
    }, [activeIndex]);

    const transitionToSlide = useCallback((newIndex: number) => {
        if (isAnimating || newIndex === activeIndex || !sliderImageRef.current || !sliderImageNextRef.current || !sliderImageBgRef.current || !transitionOverlayRef.current) return;

        setIsAnimating(true);
        const slideDirection = newIndex > activeIndex ? "right" : "left";
        const newImageUrl = `url(${TOURISM_EXPERIENCES[newIndex].imgUrl})`;

        gsap.set([sliderImageNextRef.current, sliderImageBgRef.current], { backgroundImage: newImageUrl, visibility: 'visible' });

        const xOffset = slideDirection === "right" ? "100%" : "-100%";
        gsap.set(sliderImageNextRef.current, { x: xOffset, y: 0, opacity: 1, scale: 1 });
        gsap.set(sliderImageBgRef.current, { x: xOffset, y: 0, opacity: 0.9, scale: 1 });
       
        const masterTl = gsap.timeline({
            onComplete: () => {
                gsap.set(sliderImageRef.current, { backgroundImage: newImageUrl, x: 0, opacity: 1 });
                gsap.set([sliderImageNextRef.current, sliderImageBgRef.current, transitionOverlayRef.current], {
                    opacity: 0, x: 0, y: 0, visibility: 'hidden'
                });
               
                setActiveIndex(newIndex);

                const showTl = gsap.timeline({ onComplete: () => setIsAnimating(false) });
                showTl.to(contentTitleSpanRef.current, { y: 0, duration: TIMING.BASE, ease: "sideEase" }, 0)
                      .to(contentParagraphRef.current, { opacity: 1, duration: TIMING.BASE, ease: "mainEase" }, TIMING.STAGGER_SMALL);
            }
        });

        // Hide current content
        masterTl.to(contentParagraphRef.current, { opacity: 0, duration: TIMING.SHORT, ease: "mainEase" }, 0)
                .to(contentTitleSpanRef.current, { y: "100%", duration: TIMING.SHORT, ease: "sideEase" }, TIMING.STAGGER_TINY);

        // Flash overlay effect
        masterTl.to(transitionOverlayRef.current, { opacity: 0.15, duration: TIMING.SHORTEST, ease: "power1.in" }, TIMING.STAGGER_SMALL)
                .to(transitionOverlayRef.current, { opacity: 0, duration: TIMING.SHORT, ease: "power1.out" }, TIMING.STAGGER_MED);
       
        // Parallax slide transition
        masterTl.to(sliderImageRef.current, { x: slideDirection === "right" ? "-35%" : "35%", duration: TIMING.LONG, ease: "mainEase" }, 0)
                .to(sliderImageBgRef.current, { x: slideDirection === "right" ? "-10%" : "10%", opacity: 0.95, duration: TIMING.LONG, ease: "sideEase" }, TIMING.STAGGER_TINY)
                .to(sliderImageNextRef.current, { x: 0, duration: TIMING.LONG, ease: "sideEase" }, TIMING.STAGGER_SMALL);

    }, [activeIndex, isAnimating]);


    // --- Event Handlers ---
   
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (currentMode !== 'slider' || isAnimating) return;
            if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                transitionToSlide((activeIndex + 1) % TOURISM_EXPERIENCES.length);
            } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                transitionToSlide((activeIndex - 1 + TOURISM_EXPERIENCES.length) % TOURISM_EXPERIENCES.length);
            }
        };

        let touchStartX = 0;
        const handleTouchStart = (e: TouchEvent) => {
             if (currentMode !== 'slider' || isAnimating) return;
             touchStartX = e.changedTouches[0].screenX;
        };
        const handleTouchEnd = (e: TouchEvent) => {
             if (currentMode !== 'slider' || isAnimating) return;
             const touchEndX = e.changedTouches[0].screenX;
             const swipeThreshold = 50;
             if (touchEndX < touchStartX - swipeThreshold) {
                 transitionToSlide((activeIndex + 1) % TOURISM_EXPERIENCES.length);
             } else if (touchEndX > touchStartX + swipeThreshold) {
                 transitionToSlide((activeIndex - 1 + TOURISM_EXPERIENCES.length) % TOURISM_EXPERIENCES.length);
             }
        };

        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('touchstart', handleTouchStart);
        window.addEventListener('touchend', handleTouchEnd);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('touchstart', handleTouchStart);
            window.removeEventListener('touchend', handleTouchEnd);
        };
    }, [currentMode, isAnimating, activeIndex, transitionToSlide]);

    const handleToggleView = (mode: 'grid' | 'slider') => {
        if (isAnimating || currentMode === mode) return;
        setCurrentMode(mode);
        if (mode === 'slider') {
            showSliderView();
        } else {
            showGridView();
        }
    };
   
    return (
        <div className="w-full bg-[#16191D] py-8 sm:py-12 md:py-16 lg:py-20 relative overflow-hidden">
            {/* Luxury Background Pattern */}
            <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                    backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                                     radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
                    backgroundSize: '50px 50px'
                }}></div>
            </div>

            {/* Section Title and Description - Mobile Responsive */}
            <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8 sm:mb-12 md:mb-16">
                <div className="text-center">
                    <h2 className="font-cormorant text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-semibold text-[#F2EEE6] mb-4 sm:mb-6 md:mb-8 luxury-typography"
                        style={{
                            fontFamily: 'Cormorant Garamond',
                            textShadow: '2px 2px 4px rgba(212, 194, 164, 0.3), 0 0 20px rgba(212, 194, 164, 0.2)',
                            letterSpacing: '0.02em'
                        }}>
                        Adventure Experiences
                    </h2>
                    <p className="font-open-sans text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/90 max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-3xl mx-auto leading-relaxed luxury-typography px-2 sm:px-0"
                       style={{
                           textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
                           lineHeight: '1.6'
                       }}>
                        Discover the thrill of Tanzania through our diverse range of adventure activities. From scaling Africa's highest peaks to soaring above the Serengeti, each experience is crafted to create unforgettable memories while ensuring your safety and comfort.
                    </p>
                </div>
            </div>

            {/* Interactive Gallery - Perfectly Centered & Mobile Responsive */}
            <div className="gallery-centered-container px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
                <div className="h-[250px] xs:h-[300px] sm:h-[350px] md:h-[450px] lg:h-[550px] xl:h-[650px] w-full max-w-[98%] xs:max-w-[95%] sm:max-w-[90%] md:max-w-4xl lg:max-w-5xl xl:max-w-6xl font-mono text-[#F2EEE6] bg-[#16191D] overflow-hidden relative rounded-lg sm:rounded-xl md:rounded-2xl shadow-2xl border border-[#D4C2A4]/20 luxury-scrollbar">
                {/* Luxury Glass Morphism Border */}
                <div className="absolute inset-0 rounded-lg sm:rounded-xl md:rounded-2xl bg-gradient-to-br from-[#D4C2A4]/10 via-transparent to-[#D4C2A4]/5 pointer-events-none"></div>

                {/* Preloader - Luxury Style */}
                <div ref={preloaderRef} className={`absolute inset-0 bg-gradient-to-br from-[#16191D] via-[#1a1d23] to-[#16191D] flex justify-center items-center z-[9999] transition-opacity duration-500 rounded-lg sm:rounded-xl md:rounded-2xl ${isAppReady ? 'opacity-0 invisible' : 'opacity-100 visible'}`}>
                    <div className="font-cormorant text-3xl sm:text-4xl md:text-5xl text-[#D4C2A4] luxury-typography"
                         style={{
                             textShadow: '0 0 20px rgba(212, 194, 164, 0.5)',
                             letterSpacing: '0.1em'
                         }}>
                        {preloaderCount}%
                    </div>
                </div>

            {/* Main Content (conditionally visible) */}
            <div className={`${isAppReady ? 'opacity-100' : 'opacity-0'} transition-opacity duration-700`}>
                {/* Grid View - Mobile Responsive */}
                <div className="absolute inset-0 w-full h-full overflow-hidden rounded-lg sm:rounded-xl md:rounded-2xl">
                    <div ref={gridRef} className="gallery-responsive-grid grid grid-cols-2 sm:grid-cols-3 w-full h-full gap-1 xs:gap-1.5 sm:gap-2 md:gap-3 lg:gap-4 p-2 xs:p-3 sm:p-4 md:p-5 lg:p-6">
                        {TOURISM_EXPERIENCES.map((experience, index) => (
                            <div
                                key={index}
                                ref={el => gridItemsRef.current[index] = el}
                                className={`gallery-grid-item relative overflow-hidden rounded-md sm:rounded-lg md:rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group touch-target ${
                                    // Mobile layout: simple 2-column grid, Desktop: 3-column with special positioning
                                    index === 4 ? 'sm:col-start-2 sm:row-start-2' : ''
                                }`}
                                onClick={() => {
                                    setActiveIndex(index);
                                    handleToggleView('slider');
                                }}
                                onTouchStart={(e) => e.currentTarget.style.transform = 'scale(0.98)'}
                                onTouchEnd={(e) => e.currentTarget.style.transform = 'scale(1)'}
                            >
                                {/* Luxury Overlay */}
                                <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>

                                {/* Image */}
                                <div
                                    className="w-full h-full bg-cover bg-center transition-transform duration-500 group-hover:scale-105"
                                    style={{
                                        backgroundImage: `url(${experience.imgUrl})`,
                                        backgroundSize: 'cover',
                                        backgroundPosition: 'center',
                                        backgroundRepeat: 'no-repeat'
                                    }}
                                ></div>

                                {/* Luxury Border Glow */}
                                <div className="absolute inset-0 rounded-md sm:rounded-lg md:rounded-xl border border-[#D4C2A4]/20 group-hover:border-[#D4C2A4]/40 transition-all duration-300"></div>

                                {/* Title Overlay for Mobile */}
                                <div className="absolute bottom-0 left-0 right-0 p-2 sm:p-3 bg-gradient-to-t from-[#16191D]/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                                    <h3 className="font-cormorant text-xs sm:text-sm md:text-base text-[#F2EEE6] font-semibold luxury-typography"
                                        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}>
                                        {experience.title}
                                    </h3>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Slider Layers - Luxury Style */}
                <div ref={sliderImageBgRef} className="absolute inset-0 w-full h-full bg-cover bg-center z-[85] opacity-0 invisible rounded-lg sm:rounded-xl md:rounded-2xl"></div>
                <div ref={sliderImageNextRef} className="absolute inset-0 w-full h-full bg-cover bg-center z-[90] opacity-0 invisible rounded-lg sm:rounded-xl md:rounded-2xl"></div>
                <div ref={sliderImageRef} className="absolute inset-0 w-full h-full bg-cover bg-center z-[80] opacity-0 invisible rounded-lg sm:rounded-xl md:rounded-2xl"></div>
                <div ref={transitionOverlayRef} className="absolute inset-0 w-full h-full bg-gradient-to-br from-[#16191D]/80 to-[#16191D]/60 z-[95] opacity-0 invisible rounded-lg sm:rounded-xl md:rounded-2xl"></div>

                {/* Content Overlay for Slider - Mobile Responsive */}
                <div ref={contentRef} className="absolute inset-0 z-[100] opacity-0 p-4 sm:p-6 md:p-8 lg:p-[10%] flex flex-col justify-end pointer-events-none">
                    <h1 className="text-left font-semibold text-[#F2EEE6] font-cormorant text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl uppercase tracking-tighter mb-3 sm:mb-4 md:mb-6 overflow-hidden luxury-typography"
                        style={{
                            textShadow: '2px 2px 4px rgba(212, 194, 164, 0.3), 0 0 20px rgba(212, 194, 164, 0.2), 3px 3px 6px rgba(0,0,0,0.8)',
                            letterSpacing: '0.02em'
                        }}>
                        <span ref={contentTitleSpanRef} className="block translate-y-full">
                           {TOURISM_EXPERIENCES[activeIndex].title}
                        </span>
                    </h1>
                    <div ref={contentParagraphRef} className="text-left text-[#F2EEE6]/95 font-medium font-open-sans text-xs sm:text-sm md:text-base lg:text-lg max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xl leading-relaxed mb-4 sm:mb-6 md:mb-8 lg:mb-[10%] opacity-0 luxury-typography"
                         style={{
                             textShadow: '1px 1px 2px rgba(0,0,0,0.8), 0 0 10px rgba(212, 194, 164, 0.1)',
                             lineHeight: '1.6'
                         }}>
                        {TOURISM_EXPERIENCES[activeIndex].paragraph}
                    </div>
                </div>

                {/* Thumbnails for Slider - Mobile Responsive */}
                <div className="absolute bottom-2 xs:bottom-3 sm:bottom-4 md:bottom-5 right-2 xs:right-3 sm:right-4 md:right-5 flex gap-1 xs:gap-1.5 sm:gap-2 md:gap-2.5 z-[200] flex-wrap max-w-[180px] xs:max-w-[200px] sm:max-w-none">
                    {TOURISM_EXPERIENCES.map((experience, index) => (
                        <div
                            key={index}
                            ref={el => thumbnailItemsRef.current[index] = el}
                            onClick={() => transitionToSlide(index)}
                            className={`w-[30px] h-[20px] xs:w-[35px] xs:h-[25px] sm:w-[45px] sm:h-[32px] md:w-[55px] md:h-[38px] lg:w-[60px] lg:h-[40px] rounded-sm sm:rounded-md overflow-hidden cursor-pointer opacity-0 translate-y-5 transition-all duration-300 ease-in-out hover:border-2 hover:border-[#D4C2A4]/70 hover:shadow-lg hover:shadow-[#D4C2A4]/25 hover:scale-105 touch-target ${
                                activeIndex === index
                                    ? 'border-2 border-[#D4C2A4] shadow-lg shadow-[#D4C2A4]/30'
                                    : 'border-2 border-[#D4C2A4]/20'
                            }`}
                            style={{
                                backdropFilter: 'blur(10px)',
                                background: 'linear-gradient(135deg, rgba(212, 194, 164, 0.1) 0%, rgba(212, 194, 164, 0.05) 100%)'
                            }}
                        >
                            <div className="w-full h-full bg-cover bg-center" style={{ backgroundImage: `url(${experience.imgUrl})` }}></div>

                            {/* Luxury Overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/30 to-transparent"></div>
                        </div>
                    ))}
                </div>
            </div>
                </div>
            </div>

           
        </div>
    );
};

export default MonochromeGallery;
